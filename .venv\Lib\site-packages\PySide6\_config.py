built_modules = list(name for name in
    "Core;Gui;Widgets;PrintSupport;Sql;Network;Test;Concurrent;DBus;Designer;Xml;Help;Multimedia;MultimediaWidgets;OpenGL;OpenGLWidgets;Pdf;PdfWidgets;Positioning;Location;NetworkAuth;Nfc;Qml;Quick;Quick3D;QuickControls2;QuickTest;QuickWidgets;RemoteObjects;Scxml;Sensors;SerialPort;SerialBus;StateMachine;TextToSpeech;Charts;SpatialAudio;Svg;SvgWidgets;DataVisualization;Graphs;GraphsWidgets;Bluetooth;UiTools;AxContainer;WebChannel;WebEngineCore;WebEngineWidgets;WebEngineQuick;WebSockets;HttpServer;WebView;3DCore;3DRender;3DInput;3DLogic;3DAnimation;3DExtras"
    .split(";"))

shiboken_library_soversion = "6.9"
pyside_library_soversion = "6.9"

version = "6.9.1"
version_info = (6, 9, 1, "", "")

__build_date__ = '2025-05-31T17:31:51+00:00'




__setup_py_package_version__ = '6.9.1'

