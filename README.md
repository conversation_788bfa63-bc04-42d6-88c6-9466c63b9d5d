# Gemini Quant - 个人量化交易系统

[![Python Version](https://img.shields.io/badge/python-3.13+-blue.svg)](https://python.org)
[![License](https://img.shields.io/badge/license-MIT-green.svg)](LICENSE)
[![Code Style](https://img.shields.io/badge/code%20style-black-000000.svg)](https://github.com/psf/black)

一个使用 Python 3.13 新特性构建的现代化、事件驱动的量化交易系统。

## ✨ 特性

### 🏗️ 架构特性
- **事件驱动架构 (EDA)**: 高度解耦的模块化设计
- **多进程+多线程**: 充分利用 Python 3.13 的 Free-threaded 特性
- **MVVM 模式**: 使用 PySide6 构建的现代化 GUI
- **插件化设计**: 易于扩展的策略和数据源接口

### 📊 数据处理
- **多数据源支持**: CSV、实时API、WebSocket 等
- **高效存储**: Feather/Parquet 格式，支持大数据量
- **实时 K线合成**: 从 Tick 数据生成各种周期 K线
- **数据清洗**: 自动处理异常数据和缺失值

### 🤖 策略引擎
- **回测引擎**: 高精度历史数据回测
- **实盘交易**: 无缝切换回测和实盘模式
- **AI 优化器**: 基于 Optuna 的参数优化
- **技术指标库**: 集成 TA-Lib 和 pandas-ta

### 💼 投资组合管理
- **实时持仓跟踪**: 精确的资金和持仓管理
- **风险控制**: 多层次风险管理机制
- **订单管理**: 完整的订单生命周期管理
- **绩效分析**: 详细的交易统计和分析

### 🖥️ 用户界面
- **现代化 GUI**: 基于 PySide6 的响应式界面
- **实时图表**: 使用 pyqtgraph 的高性能图表
- **可定制布局**: 支持拖拽和保存工作区布局
- **主题支持**: 明暗主题切换

## 🚀 快速开始

### 环境要求

- Python 3.13+
- 操作系统: Windows 10+, macOS 10.15+, Linux (Ubuntu 20.04+)
- 内存: 建议 8GB+
- 硬盘: 建议 10GB+ 可用空间

### 安装步骤

1. **克隆项目**
```bash
git clone https://github.com/your-username/gemini-quant.git
cd gemini-quant
```

2. **创建虚拟环境**
```bash
python -m venv venv

# Windows
venv\Scripts\activate

# macOS/Linux
source venv/bin/activate
```

3. **安装依赖**
```bash
pip install -r requirements.txt
```

4. **安装 TA-Lib (可选但推荐)**
```bash
# Windows: 下载对应版本的 whl 文件
pip install TA_Lib-0.4.25-cp313-cp313-win_amd64.whl

# macOS
brew install ta-lib
pip install TA-Lib

# Ubuntu/Debian
sudo apt-get install libta-lib-dev
pip install TA-Lib
```

5. **运行系统**
```bash
python main.py
```

### 配置说明

首次运行时，系统会在 `config/` 目录下创建默认配置文件：

- `default_config.yaml`: 默认配置（请勿修改）
- `user_config.yaml`: 用户自定义配置

主要配置项：

```yaml
# 数据服务配置
data_service:
  storage_path: "./data"  # 数据存储路径
  connectors:
    csv:
      enabled: true
      data_path: "./data/csv"

# 交易配置
execution:
  simulation_mode: true      # 模拟交易模式
  initial_capital: 1000000   # 初始资金

# GUI配置
gui:
  enabled: true
  theme: "dark"             # 主题: dark/light
  window_size: [1200, 800]  # 窗口大小
```

## 📁 项目结构

```
gemini-quant/
├── main.py                 # 程序入口
├── requirements.txt        # 依赖包列表
├── README.md              # 项目说明
├── config/                # 配置文件目录
├── data/                  # 数据存储目录
├── logs/                  # 日志文件目录
├── core/                  # 核心模块
│   ├── event_bus.py       # 事件总线
│   ├── event_types.py     # 事件类型定义
│   ├── data_types.py      # 数据类型定义
│   └── main_controller.py # 主控制器
├── data_service/          # 数据服务模块
│   ├── process.py         # 数据服务进程
│   ├── processor.py       # 数据处理器
│   ├── storage.py         # 数据存储
│   └── connectors/        # 数据连接器
├── strategy_ai_engine/    # 策略与AI引擎
│   ├── backtester.py      # 回测引擎
│   ├── live_trader.py     # 实盘引擎
│   ├── ai_optimizer.py    # AI优化器
│   └── indicators/        # 技术指标库
├── execution_portfolio/   # 交易执行与投资组合
│   ├── portfolio_manager.py # 投资组合管理
│   ├── order_executor.py  # 订单执行器
│   ├── risk_manager.py    # 风险管理器
│   └── brokers/           # 券商接口
├── gui/                   # GUI界面
│   ├── main_window.py     # 主窗口
│   ├── viewmodels/        # 视图模型
│   ├── views/             # 视图组件
│   └── widgets/           # 自定义控件
├── infrastructure/        # 基础设施
│   ├── config_manager.py  # 配置管理
│   ├── logger.py          # 日志服务
│   └── notification.py    # 通知服务
└── strategies/            # 策略目录
    ├── base_strategy.py   # 策略基类
    └── examples/          # 示例策略
```

## 🔧 开发指南

### 创建自定义策略

1. 继承 `BaseStrategy` 类：

```python
from strategies.base_strategy import BaseStrategy
from core.data_types import BarData
from core.event_types import SignalEvent

class MyStrategy(BaseStrategy):
    def __init__(self, name: str):
        super().__init__(name)
        self.sma_period = 20
    
    def on_bar(self, bar: BarData) -> None:
        # 策略逻辑
        if self.should_buy(bar):
            self.buy(bar.symbol, 100)
        elif self.should_sell(bar):
            self.sell(bar.symbol, 100)
```

2. 将策略文件放入 `strategies/` 目录
3. 在 GUI 中加载和运行策略

### 添加数据源

1. 实现数据连接器接口：

```python
from data_service.connectors.base_connector import BaseConnector

class MyDataConnector(BaseConnector):
    def connect(self) -> bool:
        # 连接逻辑
        pass
    
    def subscribe(self, symbol: str) -> None:
        # 订阅数据
        pass
```

2. 在配置文件中启用连接器
3. 系统会自动加载和使用

## 🧪 测试

运行测试套件：

```bash
# 运行所有测试
pytest

# 运行单元测试
pytest -m unit

# 运行集成测试
pytest -m integration

# 生成覆盖率报告
pytest --cov=core --cov=data_service --cov-report=html
```

## 📈 性能优化

### Python 3.13 特性利用

- **Free-threaded 模式**: 在支持的环境中启用无 GIL 模式
- **改进的错误消息**: 更好的调试体验
- **新的 typing 特性**: 更强的类型安全

### 性能监控

```bash
# 内存分析
python -m memory_profiler main.py

# 性能分析
python -m cProfile -o profile.stats main.py
```

## 🤝 贡献指南

1. Fork 项目
2. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 开启 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## ⚠️ 免责声明

本软件仅用于教育和研究目的。使用本软件进行实际交易的风险由用户自行承担。作者不对任何投资损失负责。

## 📞 联系方式

- 项目主页: [GitHub](https://github.com/your-username/gemini-quant)
- 问题反馈: [Issues](https://github.com/your-username/gemini-quant/issues)
- 邮箱: <EMAIL>

## 🙏 致谢

感谢以下开源项目的支持：
- [Python](https://python.org)
- [PySide6](https://wiki.qt.io/Qt_for_Python)
- [pandas](https://pandas.pydata.org)
- [Optuna](https://optuna.org)
- [TA-Lib](https://ta-lib.org)
