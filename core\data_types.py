"""
核心数据类型定义
使用 Python 3.13 的新 typing 特性
"""

from __future__ import annotations
from dataclasses import dataclass, field
from datetime import datetime
from decimal import Decimal
from enum import Enum, auto
from typing import TypeVar, Generic, Optional, Dict, List, Any, TypedDict

# Python 3.13 兼容性处理
try:
    from typing import ReadOnly  # Python 3.13 新特性
except ImportError:
    # 为旧版本 Python 提供兼容性
    from typing_extensions import ReadOnly


# TypeVar 定义（兼容旧版本 Python）
T = TypeVar('T')
PriceType = TypeVar('PriceType', bound=Decimal)
VolumeType = TypeVar('VolumeType', bound=int)


class Direction(Enum):
    """交易方向"""
    LONG = "LONG"
    SHORT = "SHORT"


class OrderType(Enum):
    """订单类型"""
    MARKET = "MARKET"
    LIMIT = "LIMIT"
    STOP = "STOP"
    STOP_LIMIT = "STOP_LIMIT"


class OrderStatus(Enum):
    """订单状态"""
    PENDING = auto()
    SUBMITTED = auto()
    PARTIAL_FILLED = auto()
    FILLED = auto()
    CANCELLED = auto()
    REJECTED = auto()


class Interval(Enum):
    """时间间隔"""
    TICK = "tick"
    MINUTE_1 = "1m"
    MINUTE_5 = "5m"
    MINUTE_15 = "15m"
    MINUTE_30 = "30m"
    HOUR_1 = "1h"
    HOUR_4 = "4h"
    DAY_1 = "1d"
    WEEK_1 = "1w"
    MONTH_1 = "1M"


# 使用 Python 3.13 的 ReadOnly TypedDict
class SymbolInfo(TypedDict):
    """交易品种信息"""
    symbol: ReadOnly[str]
    name: ReadOnly[str]
    exchange: ReadOnly[str]
    product_type: ReadOnly[str]
    size: ReadOnly[Decimal]
    price_tick: ReadOnly[Decimal]
    min_volume: ReadOnly[int]
    margin_rate: ReadOnly[Decimal]


@dataclass(frozen=True, slots=True)  # Python 3.10+ slots 优化
class TickData:
    """Tick数据"""
    symbol: str
    datetime: datetime
    last_price: Decimal
    volume: int
    turnover: Decimal
    open_interest: int = 0
    
    # 买卖盘数据
    bid_price_1: Decimal = Decimal('0')
    bid_volume_1: int = 0
    ask_price_1: Decimal = Decimal('0')
    ask_volume_1: int = 0
    
    # 扩展买卖盘（可选）
    bid_prices: List[Decimal] = field(default_factory=list)
    bid_volumes: List[int] = field(default_factory=list)
    ask_prices: List[Decimal] = field(default_factory=list)
    ask_volumes: List[int] = field(default_factory=list)
    
    def __post_init__(self):
        """数据验证"""
        if self.last_price <= 0:
            raise ValueError("价格必须大于0")
        if self.volume < 0:
            raise ValueError("成交量不能为负")


@dataclass(frozen=True, slots=True)
class BarData:
    """K线数据"""
    symbol: str
    datetime: datetime
    interval: Interval
    
    open_price: Decimal
    high_price: Decimal
    low_price: Decimal
    close_price: Decimal
    volume: int
    turnover: Decimal
    open_interest: int = 0
    
    def __post_init__(self):
        """数据验证"""
        prices = [self.open_price, self.high_price, self.low_price, self.close_price]
        if any(p <= 0 for p in prices):
            raise ValueError("所有价格必须大于0")
        if self.high_price < max(self.open_price, self.close_price):
            raise ValueError("最高价不能小于开盘价或收盘价")
        if self.low_price > min(self.open_price, self.close_price):
            raise ValueError("最低价不能大于开盘价或收盘价")


@dataclass(frozen=True, slots=True)
class OrderData:
    """订单数据"""
    symbol: str
    order_id: str
    direction: Direction
    order_type: OrderType
    volume: int
    price: Decimal
    status: OrderStatus
    datetime: datetime
    
    # 可选字段
    filled_volume: int = 0
    avg_price: Decimal = Decimal('0')
    commission: Decimal = Decimal('0')
    error_msg: str = ""
    
    def __post_init__(self):
        """数据验证"""
        if self.volume <= 0:
            raise ValueError("订单数量必须大于0")
        if self.price <= 0 and self.order_type != OrderType.MARKET:
            raise ValueError("限价单价格必须大于0")


@dataclass(frozen=True, slots=True)
class TradeData:
    """成交数据"""
    symbol: str
    trade_id: str
    order_id: str
    direction: Direction
    volume: int
    price: Decimal
    datetime: datetime
    commission: Decimal = Decimal('0')


@dataclass(slots=True)
class PositionData:
    """持仓数据"""
    symbol: str
    direction: Direction
    volume: int
    frozen: int
    price: Decimal
    pnl: Decimal
    datetime: datetime
    
    def __post_init__(self):
        """数据验证"""
        if self.volume < 0:
            raise ValueError("持仓数量不能为负")
        if self.frozen < 0:
            raise ValueError("冻结数量不能为负")


@dataclass(slots=True)
class AccountData:
    """账户数据"""
    account_id: str
    balance: Decimal
    frozen: Decimal
    available: Decimal
    datetime: datetime
    
    def __post_init__(self):
        """数据验证和计算"""
        if self.balance < 0:
            raise ValueError("账户余额不能为负")
        # 自动计算可用资金
        object.__setattr__(self, 'available', self.balance - self.frozen)


# 泛型数据容器，使用 Python 3.13 的改进泛型
@dataclass(frozen=True, slots=True)
class DataContainer(Generic[T]):
    """通用数据容器"""
    data: T
    timestamp: datetime
    source: str = "unknown"
    
    def copy_with(self, **changes) -> DataContainer[T]:
        """使用 Python 3.13 的 copy.replace 概念创建副本"""
        from copy import replace
        return replace(self, **changes)
