"""
事件类型定义
使用 Python 3.13 的新特性实现类型安全的事件系统
"""

from __future__ import annotations
from abc import ABC, abstractmethod
from dataclasses import dataclass, field
from datetime import datetime
from typing import TypeVar, Generic, Any, Dict, Optional, Type, ClassVar
from uuid import uuid4

from .data_types import TickData, BarData, OrderData, TradeData, PositionData, AccountData


# 事件类型变量
EventType = TypeVar('EventType', bound='BaseEvent')


@dataclass(frozen=True, slots=True)
class BaseEvent(ABC):
    """事件基类"""
    event_id: str = field(default_factory=lambda: str(uuid4()))
    timestamp: datetime = field(default_factory=datetime.now)
    source: str = "system"
    
    # 类变量，用于事件类型标识
    EVENT_TYPE: ClassVar[str] = "base"
    
    @classmethod
    def get_event_type(cls) -> str:
        """获取事件类型"""
        return cls.EVENT_TYPE


# 数据事件
@dataclass(frozen=True, slots=True)
class TickEvent(BaseEvent):
    """Tick数据事件"""
    EVENT_TYPE: ClassVar[str] = "tick"
    data: TickData


@dataclass(frozen=True, slots=True)
class BarEvent(BaseEvent):
    """K线数据事件"""
    EVENT_TYPE: ClassVar[str] = "bar"
    data: BarData


# 交易事件
@dataclass(frozen=True, slots=True)
class SignalEvent(BaseEvent):
    """交易信号事件"""
    EVENT_TYPE: ClassVar[str] = "signal"
    symbol: str
    direction: str  # "LONG" or "SHORT"
    volume: int
    price: Optional[float] = None  # None表示市价单
    strategy_name: str = "unknown"
    signal_strength: float = 1.0  # 信号强度 0-1


@dataclass(frozen=True, slots=True)
class OrderEvent(BaseEvent):
    """订单事件"""
    EVENT_TYPE: ClassVar[str] = "order"
    data: OrderData


@dataclass(frozen=True, slots=True)
class TradeEvent(BaseEvent):
    """成交事件"""
    EVENT_TYPE: ClassVar[str] = "trade"
    data: TradeData


@dataclass(frozen=True, slots=True)
class OrderUpdateEvent(BaseEvent):
    """订单状态更新事件"""
    EVENT_TYPE: ClassVar[str] = "order_update"
    data: OrderData


@dataclass(frozen=True, slots=True)
class FillEvent(BaseEvent):
    """成交回报事件"""
    EVENT_TYPE: ClassVar[str] = "fill"
    data: TradeData


# 投资组合事件
@dataclass(frozen=True, slots=True)
class PositionUpdateEvent(BaseEvent):
    """持仓更新事件"""
    EVENT_TYPE: ClassVar[str] = "position_update"
    data: PositionData


@dataclass(frozen=True, slots=True)
class AccountUpdateEvent(BaseEvent):
    """账户更新事件"""
    EVENT_TYPE: ClassVar[str] = "account_update"
    data: AccountData


# 系统事件
@dataclass(frozen=True, slots=True)
class LogEvent(BaseEvent):
    """日志事件"""
    EVENT_TYPE: ClassVar[str] = "log"
    level: str  # DEBUG, INFO, WARNING, ERROR, CRITICAL
    message: str
    module: str = "system"
    extra_data: Dict[str, Any] = field(default_factory=dict)


@dataclass(frozen=True, slots=True)
class RiskWarningEvent(BaseEvent):
    """风险警告事件"""
    EVENT_TYPE: ClassVar[str] = "risk_warning"
    risk_type: str  # "position_limit", "loss_limit", "margin_call", etc.
    message: str
    severity: str = "WARNING"  # WARNING, CRITICAL
    data: Dict[str, Any] = field(default_factory=dict)


@dataclass(frozen=True, slots=True)
class SystemStatusEvent(BaseEvent):
    """系统状态事件"""
    EVENT_TYPE: ClassVar[str] = "system_status"
    status: str  # "starting", "running", "stopping", "stopped", "error"
    message: str = ""
    component: str = "system"


@dataclass(frozen=True, slots=True)
class StrategyStatusEvent(BaseEvent):
    """策略状态事件"""
    EVENT_TYPE: ClassVar[str] = "strategy_status"
    strategy_name: str
    status: str  # "starting", "running", "stopping", "stopped", "error"
    message: str = ""


# AI优化事件
@dataclass(frozen=True, slots=True)
class OptimizationStartEvent(BaseEvent):
    """优化开始事件"""
    EVENT_TYPE: ClassVar[str] = "optimization_start"
    strategy_name: str
    parameters: Dict[str, Any]
    optimization_id: str


@dataclass(frozen=True, slots=True)
class OptimizationProgressEvent(BaseEvent):
    """优化进度事件"""
    EVENT_TYPE: ClassVar[str] = "optimization_progress"
    optimization_id: str
    progress: float  # 0.0 - 1.0
    current_params: Dict[str, Any]
    current_result: Dict[str, float]
    best_result: Dict[str, float]


@dataclass(frozen=True, slots=True)
class OptimizationCompleteEvent(BaseEvent):
    """优化完成事件"""
    EVENT_TYPE: ClassVar[str] = "optimization_complete"
    optimization_id: str
    best_params: Dict[str, Any]
    best_result: Dict[str, float]
    all_results: list = field(default_factory=list)


# 数据服务事件
@dataclass(frozen=True, slots=True)
class DataServiceStatusEvent(BaseEvent):
    """数据服务状态事件"""
    EVENT_TYPE: ClassVar[str] = "data_service_status"
    service_name: str
    status: str  # "connected", "disconnected", "error", "reconnecting"
    message: str = ""


@dataclass(frozen=True, slots=True)
class MarketStatusEvent(BaseEvent):
    """市场状态事件"""
    EVENT_TYPE: ClassVar[str] = "market_status"
    market: str
    status: str  # "pre_open", "open", "close", "break"
    next_status_time: Optional[datetime] = None


# 通知事件
@dataclass(frozen=True, slots=True)
class NotificationEvent(BaseEvent):
    """通知事件"""
    EVENT_TYPE: ClassVar[str] = "notification"
    title: str
    message: str
    notification_type: str = "info"  # "info", "warning", "error", "success"
    urgent: bool = False
    actions: list = field(default_factory=list)


# 事件类型注册表
EVENT_TYPE_REGISTRY: Dict[str, Type[BaseEvent]] = {
    "tick": TickEvent,
    "bar": BarEvent,
    "signal": SignalEvent,
    "order": OrderEvent,
    "trade": TradeEvent,
    "order_update": OrderUpdateEvent,
    "fill": FillEvent,
    "position_update": PositionUpdateEvent,
    "account_update": AccountUpdateEvent,
    "log": LogEvent,
    "risk_warning": RiskWarningEvent,
    "system_status": SystemStatusEvent,
    "strategy_status": StrategyStatusEvent,
    "optimization_start": OptimizationStartEvent,
    "optimization_progress": OptimizationProgressEvent,
    "optimization_complete": OptimizationCompleteEvent,
    "data_service_status": DataServiceStatusEvent,
    "market_status": MarketStatusEvent,
    "notification": NotificationEvent,
}


def get_event_class(event_type: str) -> Type[BaseEvent]:
    """根据事件类型获取事件类"""
    if event_type not in EVENT_TYPE_REGISTRY:
        raise ValueError(f"未知的事件类型: {event_type}")
    return EVENT_TYPE_REGISTRY[event_type]


def register_event_type(event_class: Type[BaseEvent]) -> None:
    """注册新的事件类型"""
    event_type = event_class.EVENT_TYPE
    if event_type in EVENT_TYPE_REGISTRY:
        raise ValueError(f"事件类型 {event_type} 已经注册")
    EVENT_TYPE_REGISTRY[event_type] = event_class
