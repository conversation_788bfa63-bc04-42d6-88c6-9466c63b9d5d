"""
主窗口
基于MVVM模式的GUI主窗口实现
"""

from __future__ import annotations
from typing import Any, Optional, Dict, List
import threading
import time
import logging
from datetime import datetime

from core.event_types import (
    BaseEvent, TickEvent, BarEvent, OrderEvent, TradeEvent,
    PositionUpdateEvent, AccountUpdateEvent, LogEvent
)
from core.data_types import TickData, BarData, OrderData, TradeData, PositionData, AccountData


class ConsoleGUI:
    """
    控制台GUI实现
    在没有图形界面库的情况下，使用控制台显示系统状态
    """

    def __init__(self, event_bus: Any, config: Any):
        self.event_bus = event_bus
        self.config = config
        self.logger = logging.getLogger(__name__)

        # GUI状态
        self._running = False
        self._display_thread: Optional[threading.Thread] = None

        # 数据缓存
        self.latest_ticks: Dict[str, TickData] = {}
        self.latest_bars: Dict[str, BarData] = {}
        self.orders: List[OrderData] = []
        self.trades: List[TradeData] = []
        self.positions: List[PositionData] = []
        self.account: Optional[AccountData] = None
        self.log_messages: List[str] = []

        # 显示配置
        self.max_log_lines = 20
        self.max_orders = 10
        self.max_trades = 10
        self.refresh_interval = 2.0  # 秒

    def start(self) -> None:
        """启动GUI"""
        if self._running:
            self.logger.warning("GUI已在运行")
            return

        try:
            self.logger.info("启动控制台GUI...")

            # 订阅事件
            self._subscribe_events()

            # 启动显示线程
            self._start_display_thread()

            self._running = True
            self.logger.info("控制台GUI启动成功")

        except Exception as e:
            self.logger.error(f"启动GUI失败: {e}")
            raise

    def stop(self) -> None:
        """停止GUI"""
        if not self._running:
            return

        try:
            self.logger.info("停止控制台GUI...")
            self._running = False

            # 停止显示线程
            if self._display_thread and self._display_thread.is_alive():
                self._display_thread.join(timeout=3)

            self.logger.info("控制台GUI已停止")

        except Exception as e:
            self.logger.error(f"停止GUI失败: {e}")

    def run(self) -> None:
        """运行GUI主循环"""
        self.logger.info("进入GUI主循环")

        try:
            # 显示欢迎信息
            self._show_welcome()

            # 主循环
            while self._running:
                time.sleep(1)

        except KeyboardInterrupt:
            self.logger.info("收到键盘中断信号")
        except Exception as e:
            self.logger.error(f"GUI主循环异常: {e}")
        finally:
            self.stop()

    def _subscribe_events(self) -> None:
        """订阅事件"""
        if self.event_bus:
            self.event_bus.subscribe("tick", self._on_tick_event, "gui_tick")
            self.event_bus.subscribe("bar", self._on_bar_event, "gui_bar")
            self.event_bus.subscribe("order", self._on_order_event, "gui_order")
            self.event_bus.subscribe("trade", self._on_trade_event, "gui_trade")
            self.event_bus.subscribe("position_update", self._on_position_event, "gui_position")
            self.event_bus.subscribe("account_update", self._on_account_event, "gui_account")
            self.event_bus.subscribe("log", self._on_log_event, "gui_log")

    def _on_tick_event(self, event: TickEvent) -> None:
        """处理Tick事件"""
        self.latest_ticks[event.data.symbol] = event.data

    def _on_bar_event(self, event: BarEvent) -> None:
        """处理K线事件"""
        self.latest_bars[event.data.symbol] = event.data

    def _on_order_event(self, event: OrderEvent) -> None:
        """处理订单事件"""
        # 更新或添加订单
        for i, order in enumerate(self.orders):
            if order.order_id == event.data.order_id:
                self.orders[i] = event.data
                return

        # 新订单
        self.orders.append(event.data)

        # 限制订单数量
        if len(self.orders) > self.max_orders:
            self.orders = self.orders[-self.max_orders:]

    def _on_trade_event(self, event: TradeEvent) -> None:
        """处理成交事件"""
        self.trades.append(event.data)

        # 限制成交记录数量
        if len(self.trades) > self.max_trades:
            self.trades = self.trades[-self.max_trades:]

    def _on_position_event(self, event: PositionUpdateEvent) -> None:
        """处理持仓事件"""
        # 更新或添加持仓
        for i, position in enumerate(self.positions):
            if (position.symbol == event.data.symbol and
                position.direction == event.data.direction):
                self.positions[i] = event.data
                return

        # 新持仓
        if event.data.volume > 0:
            self.positions.append(event.data)

    def _on_account_event(self, event: AccountUpdateEvent) -> None:
        """处理账户事件"""
        self.account = event.data

    def _on_log_event(self, event: LogEvent) -> None:
        """处理日志事件"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        log_line = f"[{timestamp}] {event.level}: {event.message}"
        self.log_messages.append(log_line)

        # 限制日志行数
        if len(self.log_messages) > self.max_log_lines:
            self.log_messages = self.log_messages[-self.max_log_lines:]

    def _start_display_thread(self) -> None:
        """启动显示线程"""
        def display_worker():
            """显示工作线程"""
            while self._running:
                try:
                    self._update_display()
                    time.sleep(self.refresh_interval)
                except Exception as e:
                    self.logger.error(f"显示线程异常: {e}")
                    time.sleep(1)

        self._display_thread = threading.Thread(
            target=display_worker,
            name="GUI-Display",
            daemon=True
        )
        self._display_thread.start()

    def _show_welcome(self) -> None:
        """显示欢迎信息"""
        print("\n" + "=" * 80)
        print("🚀 Gemini Quant - 量化交易系统")
        print("=" * 80)
        print("系统正在运行，按 Ctrl+C 退出")
        print("=" * 80 + "\n")

    def _update_display(self) -> None:
        """更新显示"""
        # 清屏（简单实现）
        import os
        if os.name == 'nt':  # Windows
            os.system('cls')
        else:  # Unix/Linux/macOS
            os.system('clear')

        # 显示标题
        print("🚀 Gemini Quant - 实时监控面板")
        print("=" * 80)
        print(f"更新时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print("=" * 80)

        # 显示账户信息
        self._display_account()

        # 显示持仓信息
        self._display_positions()

        # 显示最新价格
        self._display_prices()

        # 显示最近订单
        self._display_orders()

        # 显示最近成交
        self._display_trades()

        # 显示日志
        self._display_logs()

        print("=" * 80)
        print("按 Ctrl+C 退出系统")

    def _display_account(self) -> None:
        """显示账户信息"""
        print("\n💰 账户信息:")
        print("-" * 40)

        if self.account:
            print(f"账户余额: {self.account.balance:,.2f}")
            print(f"可用资金: {self.account.available:,.2f}")
            print(f"冻结资金: {self.account.frozen:,.2f}")
        else:
            print("暂无账户信息")

    def _display_positions(self) -> None:
        """显示持仓信息"""
        print("\n📊 持仓信息:")
        print("-" * 60)

        if self.positions:
            print(f"{'品种':<10} {'方向':<6} {'数量':<8} {'均价':<10} {'盈亏':<12}")
            print("-" * 60)
            for pos in self.positions:
                if pos.volume > 0:
                    print(f"{pos.symbol:<10} {pos.direction.value:<6} {pos.volume:<8} "
                          f"{pos.price:<10.2f} {pos.pnl:<12.2f}")
        else:
            print("暂无持仓")

    def _display_prices(self) -> None:
        """显示最新价格"""
        print("\n📈 最新价格:")
        print("-" * 50)

        if self.latest_ticks:
            print(f"{'品种':<10} {'最新价':<10} {'成交量':<10} {'时间':<12}")
            print("-" * 50)
            for symbol, tick in list(self.latest_ticks.items())[-5:]:  # 显示最近5个
                time_str = tick.datetime.strftime("%H:%M:%S")
                print(f"{symbol:<10} {tick.last_price:<10.2f} {tick.volume:<10} {time_str:<12}")
        else:
            print("暂无价格数据")

    def _display_orders(self) -> None:
        """显示最近订单"""
        print("\n📋 最近订单:")
        print("-" * 70)

        if self.orders:
            print(f"{'订单号':<12} {'品种':<8} {'方向':<6} {'数量':<8} {'价格':<10} {'状态':<10}")
            print("-" * 70)
            for order in self.orders[-5:]:  # 显示最近5个
                print(f"{order.order_id[-8:]:<12} {order.symbol:<8} {order.direction.value:<6} "
                      f"{order.volume:<8} {order.price:<10.2f} {order.status.value:<10}")
        else:
            print("暂无订单")

    def _display_trades(self) -> None:
        """显示最近成交"""
        print("\n💼 最近成交:")
        print("-" * 70)

        if self.trades:
            print(f"{'成交号':<12} {'品种':<8} {'方向':<6} {'数量':<8} {'价格':<10} {'时间':<12}")
            print("-" * 70)
            for trade in self.trades[-5:]:  # 显示最近5个
                time_str = trade.datetime.strftime("%H:%M:%S")
                print(f"{trade.trade_id[-8:]:<12} {trade.symbol:<8} {trade.direction.value:<6} "
                      f"{trade.volume:<8} {trade.price:<10.2f} {time_str:<12}")
        else:
            print("暂无成交")

    def _display_logs(self) -> None:
        """显示日志"""
        print("\n📝 系统日志:")
        print("-" * 80)

        if self.log_messages:
            for log_line in self.log_messages[-10:]:  # 显示最近10条
                print(log_line)
        else:
            print("暂无日志")


# 主窗口类（兼容性）
class MainWindow(ConsoleGUI):
    """主窗口类，继承自控制台GUI"""
    pass
