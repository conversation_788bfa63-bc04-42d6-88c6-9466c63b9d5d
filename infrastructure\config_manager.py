"""
配置管理器
使用 Python 3.13 的新特性实现灵活的配置管理
"""

from __future__ import annotations
import json
import yaml
from pathlib import Path
from typing import Dict, Any, Optional, Union, TypeVar, Generic
from dataclasses import dataclass, field
import logging

# 使用 Python 3.13 的 TypeVar 默认值
ConfigValue = TypeVar('ConfigValue', default=Any)


@dataclass
class ConfigSection(Generic[ConfigValue]):
    """配置节"""
    name: str
    data: Dict[str, ConfigValue] = field(default_factory=dict)
    description: str = ""
    
    def get(self, key: str, default: ConfigValue = None) -> ConfigValue:
        """获取配置值"""
        return self.data.get(key, default)
    
    def set(self, key: str, value: ConfigValue) -> None:
        """设置配置值"""
        self.data[key] = value
    
    def update(self, data: Dict[str, ConfigValue]) -> None:
        """更新配置数据"""
        self.data.update(data)


class ConfigManager:
    """
    配置管理器
    
    特性：
    1. 支持多种配置文件格式（JSON, YAML）
    2. 分层配置（默认配置 + 用户配置）
    3. 配置热重载
    4. 配置验证
    5. 环境变量覆盖
    """
    
    def __init__(self, config_dir: Optional[Path] = None):
        self._logger = logging.getLogger(__name__)
        
        # 配置目录
        if config_dir is None:
            config_dir = Path(__file__).parent.parent / "config"
        self._config_dir = Path(config_dir)
        self._config_dir.mkdir(exist_ok=True)
        
        # 配置文件路径
        self._default_config_file = self._config_dir / "default_config.yaml"
        self._user_config_file = self._config_dir / "user_config.yaml"
        
        # 配置数据
        self._config: Dict[str, Any] = {}
        self._sections: Dict[str, ConfigSection] = {}
        
        # 加载配置
        self._load_config()
    
    def _load_config(self) -> None:
        """加载配置"""
        # 1. 加载默认配置
        default_config = self._load_default_config()
        
        # 2. 加载用户配置
        user_config = self._load_user_config()
        
        # 3. 合并配置
        self._config = self._merge_config(default_config, user_config)
        
        # 4. 应用环境变量覆盖
        self._apply_env_overrides()
        
        # 5. 创建配置节
        self._create_sections()
        
        self._logger.info("配置加载完成")
    
    def _load_default_config(self) -> Dict[str, Any]:
        """加载默认配置"""
        if not self._default_config_file.exists():
            self._create_default_config()
        
        return self._load_config_file(self._default_config_file)
    
    def _load_user_config(self) -> Dict[str, Any]:
        """加载用户配置"""
        if not self._user_config_file.exists():
            return {}
        
        return self._load_config_file(self._user_config_file)
    
    def _load_config_file(self, file_path: Path) -> Dict[str, Any]:
        """加载配置文件"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                if file_path.suffix.lower() == '.json':
                    return json.load(f)
                elif file_path.suffix.lower() in ['.yaml', '.yml']:
                    return yaml.safe_load(f) or {}
                else:
                    raise ValueError(f"不支持的配置文件格式: {file_path.suffix}")
        except Exception as e:
            self._logger.error(f"加载配置文件失败 {file_path}: {e}")
            return {}
    
    def _create_default_config(self) -> None:
        """创建默认配置文件"""
        default_config = {
            # 系统配置
            'system': {
                'name': '量化交易系统',
                'version': '1.0.0',
                'debug': False,
                'log_level': 'INFO'
            },
            
            # 事件总线配置
            'event_bus': {
                'max_workers': 4,
                'queue_size': 10000
            },
            
            # 数据服务配置
            'data_service': {
                'enabled': True,
                'storage_type': 'feather',  # feather, parquet, hdf5
                'storage_path': './data',
                'cache_size': 1000,
                'connectors': {
                    'csv': {
                        'enabled': True,
                        'data_path': './data/csv'
                    }
                }
            },
            
            # 策略引擎配置
            'strategy_engine': {
                'enabled': True,
                'strategy_path': './strategies',
                'max_strategies': 10
            },
            
            # 交易执行配置
            'execution': {
                'enabled': True,
                'simulation_mode': True,
                'initial_capital': 1000000.0,
                'commission_rate': 0.0003
            },
            
            # 风险管理配置
            'risk_management': {
                'enabled': True,
                'max_position_size': 0.1,  # 最大仓位比例
                'max_daily_loss': 0.05,    # 最大日损失比例
                'max_drawdown': 0.2        # 最大回撤比例
            },
            
            # GUI配置
            'gui': {
                'enabled': True,
                'theme': 'dark',
                'window_size': [1200, 800],
                'auto_save_layout': True
            },
            
            # 日志配置
            'logging': {
                'level': 'INFO',
                'file_path': './logs/quant_trader.log',
                'max_file_size': '10MB',
                'backup_count': 5,
                'format': '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            },
            
            # AI优化器配置
            'ai_optimizer': {
                'enabled': True,
                'backend': 'optuna',
                'n_trials': 100,
                'timeout': 3600  # 1小时
            }
        }
        
        self._save_config_file(self._default_config_file, default_config)
        self._logger.info(f"创建默认配置文件: {self._default_config_file}")
    
    def _save_config_file(self, file_path: Path, config: Dict[str, Any]) -> None:
        """保存配置文件"""
        try:
            with open(file_path, 'w', encoding='utf-8') as f:
                if file_path.suffix.lower() == '.json':
                    json.dump(config, f, indent=2, ensure_ascii=False)
                elif file_path.suffix.lower() in ['.yaml', '.yml']:
                    yaml.dump(config, f, default_flow_style=False, allow_unicode=True)
        except Exception as e:
            self._logger.error(f"保存配置文件失败 {file_path}: {e}")
    
    def _merge_config(self, default: Dict[str, Any], user: Dict[str, Any]) -> Dict[str, Any]:
        """合并配置"""
        result = default.copy()
        
        for key, value in user.items():
            if key in result and isinstance(result[key], dict) and isinstance(value, dict):
                result[key] = self._merge_config(result[key], value)
            else:
                result[key] = value
        
        return result
    
    def _apply_env_overrides(self) -> None:
        """应用环境变量覆盖"""
        import os
        
        # 支持的环境变量前缀
        prefix = "QUANT_"
        
        for key, value in os.environ.items():
            if key.startswith(prefix):
                # 转换环境变量名为配置路径
                config_path = key[len(prefix):].lower().split('_')
                self._set_nested_value(self._config, config_path, value)
    
    def _set_nested_value(self, config: Dict[str, Any], path: list, value: str) -> None:
        """设置嵌套配置值"""
        current = config
        for key in path[:-1]:
            if key not in current:
                current[key] = {}
            current = current[key]
        
        # 尝试转换值类型
        final_key = path[-1]
        try:
            # 尝试转换为数字
            if '.' in value:
                current[final_key] = float(value)
            else:
                current[final_key] = int(value)
        except ValueError:
            # 尝试转换为布尔值
            if value.lower() in ['true', 'false']:
                current[final_key] = value.lower() == 'true'
            else:
                current[final_key] = value
    
    def _create_sections(self) -> None:
        """创建配置节"""
        for section_name, section_data in self._config.items():
            if isinstance(section_data, dict):
                self._sections[section_name] = ConfigSection(
                    name=section_name,
                    data=section_data
                )
    
    def get(self, key: str, default: Any = None) -> Any:
        """获取配置值"""
        keys = key.split('.')
        current = self._config
        
        for k in keys:
            if isinstance(current, dict) and k in current:
                current = current[k]
            else:
                return default
        
        return current
    
    def set(self, key: str, value: Any) -> None:
        """设置配置值"""
        keys = key.split('.')
        current = self._config
        
        for k in keys[:-1]:
            if k not in current:
                current[k] = {}
            current = current[k]
        
        current[keys[-1]] = value
    
    def get_section(self, section_name: str) -> Optional[ConfigSection]:
        """获取配置节"""
        return self._sections.get(section_name)
    
    def save_user_config(self) -> None:
        """保存用户配置"""
        self._save_config_file(self._user_config_file, self._config)
    
    def reload(self) -> None:
        """重新加载配置"""
        self._load_config()
    
    def get_config_dir(self) -> Path:
        """获取配置目录"""
        return self._config_dir
