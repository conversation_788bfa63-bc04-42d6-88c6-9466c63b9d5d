#!/usr/bin/env python3
"""
个人量化交易系统主入口
使用 Python 3.13 新特性构建的事件驱动量化交易平台
"""

import sys
import signal
import asyncio
from pathlib import Path

# 添加项目根目录到 Python 路径
PROJECT_ROOT = Path(__file__).parent
sys.path.insert(0, str(PROJECT_ROOT))

from core.main_controller import MainController
from infrastructure.logger import setup_logger


def signal_handler(signum, frame):
    """优雅关闭信号处理器"""
    print(f"\n收到信号 {signum}，正在优雅关闭系统...")
    sys.exit(0)


def main():
    """主函数"""
    # 设置信号处理器
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    
    # 初始化日志系统
    logger = setup_logger()
    logger.info("启动量化交易系统...")
    
    try:
        # 创建并启动主控制器
        controller = MainController()
        controller.start()
        
    except Exception as e:
        logger.error(f"系统启动失败: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
