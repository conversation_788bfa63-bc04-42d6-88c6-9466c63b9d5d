## 个人量化交易系统架构设计文档 (v1.0)

**核心原则**: 深度模块隐藏复杂性，构建高内聚、低耦合的事件驱动系统。
**技术栈**: Python, PySide6, QtAwesome, pyqtgraph

### 1. 架构总览

本系统是一个采用**事件驱动架构 (Event-Driven Architecture, EDA)** 的单体应用，但其内部模块在逻辑上是高度解耦的微服务。所有模块通过一个中央的**事件总线 (Event Bus)**进行异步通信，互不直接依赖。这种设计确保了极高的灵活性和可扩展性。

**核心并发模型**:
为了确保GUI的流畅性和后台任务的高效性，我们将采用多进程+多线程的混合并发模型：

*   **主进程**: 运行GUI事件循环 (Qt Event Loop) 和核心事件总线。保持UI的绝对响应。
*   **数据服务进程**: 运行在独立的**子进程 (`multiprocessing.Process`)**中。这可以完全绕开GIL，并使数据服务与主应用隔离，即使数据接口崩溃也不会影响主程序。
*   **策略回测/AI优化进程**: 同样运行在独立的**子进程**中。CPU密集型的回测和AI优化任务不会阻塞GUI。
*   **工作线程 (`QThread`)**: 在主进程中用于处理非阻塞的IO密集型任务，如日志写入、配置保存等。


*(这是一个概念图，展示了事件流和模块间的关系)*

---

#### 2.1 数据服务模块 (Data Service) - 系统的血液

这是整个系统的基石。它的设计好坏直接决定了回测的准确性和实盘的稳定性。此模块应**完全独立**，即使没有GUI，它也能独立运行，为其他程序提供数据。

*   **核心增强：独立进程与健壮性**
    *   为达到最高的健壮性和隔离性，整个数据服务模块将运行在一个**独立的进程**中。这可以防止数据源的任何故障（如网络中断、API错误）影响到主程序（如GUI和策略引擎）的稳定性。
    *   **进程间通信 (IPC)**: 该独立的数据服务进程将通过高效的进程间通信管道（如 Python 的 `multiprocessing.Queue`）将标准化的数据事件（`BarEvent`, `TickEvent`）发送出来。主进程中会有一个专用的“桥接器” (`QueueBridge`) 负责监听此队列，一旦收到数据，就立即将其转发到主程序内部的事件总线，供其他模块消费。

*   **2.1.1 数据接入层 (Data Feeder / Connector)**
    *   **职责**: 连接各种外部数据源，获取最原始的数据。
    *   **设计**: 采用**适配器模式 (Adapter Pattern)**。为每一种数据源（如CTP、IB、Binance WebSocket、本地CSV文件）实现一个独立的适配器。所有适配器都实现一个统一的接口，如 `connect()`, `subscribe(symbol)`, `disconnect()`。每个适配器都是一个独立的 `.py` 文件，使其极易新增和维护。
    *   **产出**: 原始、未经处理的数据流（如Tick、逐笔委托、快照）。

*   **2.1.2 数据处理与整合层 (Data Processor)**
    *   **职责**: 将接入层的原始数据清洗、转换并合成为统一的、标准化的内部数据格式。
    *   **核心功能**:
        *   **K线合成**: 将Tick数据合成为标准时间周期的K线（1分钟、5分钟、日线等）。这是核心算法之一。该K线合成器（Bar Generator）必须足够健壮，能够正确处理实时Tick流和历史Tick数据，并能应对跨日、节假日、交易时段不连续等复杂情况。
        *   **数据清洗**: 处理重复数据、错误数据、填补缺失数据（可选）。
        *   **格式统一**: 将不同来源的数据（如股票、期货、加密货币）统一为内部标准对象（`BarData`, `TickData`, `OrderBookData`）。

*   **2.1.3 数据存储层 (Data Storage)**
    *   **职责**: 高效地存储和读取海量历史数据。
    *   **技术选型**:
        *   **高频Tick数据**: 推荐使用专业的时序数据库（如 **InfluxDB**, **TimescaleDB**）或高性能文件格式（如 **HDF5**, **Parquet**, **Feather**）。文件存储在本地查询速度最快，但管理稍复杂。
        *   **K线数据**: 时序数据库或文件格式均可。
        *   **配置/元数据**: 轻量级数据库（如 **SQLite**）或简单的配置文件（JSON/YAML）。
    *   **推荐实践**: 对于个人或中小型系统，为了简化部署和运维，强烈推荐使用**本地文件存储方案**。使用 **Feather** 或 **Parquet** 格式，它们由 `pandas/pyarrow` 库原生支持，I/O性能极高，完全足以满足分钟级甚至更高频率的回测需求，且无需额外安装和维护数据库服务。同时，使用 **SQLite** 存储策略配置、交易记录、回测报告等关系型元数据，轻便而高效。

*   **2.1.4 数据访问API (Data API)**
    *   **职责**: 为系统其他模块（主要是策略引擎**和GUI图表模块**）提供一个稳定、高效、统一的数据查询接口。
    *   **设计**: 定义清晰的函数，如 `get_bars(symbol, interval, start_dt, end_dt)`。这个API将底层存储实现（数据库或文件）完全封装起来，未来更换数据库对上层应用无影响。GUI的图表组件在需要加载历史K线进行显示时，也会调用此API。

#### 2.2 策略与AI引擎模块 (Strategy & AI Engine) - 系统的大脑

这是系统产生智能决策、创造价值的核心。

*   **2.2.1 回测引擎 (Backtesting Engine)**
    *   **职责**: 在历史数据上模拟策略交易，生成绩效报告。
    *   **核心设计**: **事件驱动型回测**。引擎不“预知”未来数据，而是逐根K线（或Tick）地“播放”历史数据，模拟真实环境。它负责处理虚拟成交、计算滑点、手续费，并更新账户净值。
    *   **性能优化**: 为避免在进行大规模或高频回测时冻结GUI主线程，回测任务将在一个**独立的子进程**中运行。回测结果将通过进程间通信返回给主进程进行展示。

*   **2.2.2 实盘引擎 (Live Trading Engine)**
    *   **职责**: 监听实时数据事件，驱动策略逻辑，并生成交易信号。
    *   **设计**: 逻辑与回测引擎高度相似，共享绝大部分代码，仅数据源（订阅实时事件而非历史事件）和订单执行的连接点不同。这种设计可以最大程度保证回测与实盘的一致性（Backtest-Live Parity）。

*   **2.2.3 策略加载器 (Strategy Loader)**
    *   **职责**: 动态加载用户编写的策略文件（如Python脚本），实例化策略对象，并管理其生命周期（初始化、启动、停止）。
    *   **实现方式**: 策略应通过继承一个统一的基类（如 `BaseStrategy`）来实现。该基类预先定义了策略的生命周期方法，如 `on_init()` (初始化), `on_start()` (策略启动), `on_bar()` / `on_tick()` (数据更新), 和 `on_stop()` (策略停止)，强制用户在标准框架内编写逻辑。

*   **2.2.4 指标库 (Indicator Library)**
    *   **职责**: 提供常用的技术分析指标（SMA, EMA, MACD, RSI等）。可以封装成熟的第三方库（如 **TA-Lib**, **Pandas-TA**），提供统一、易用的接口。

*   **2.2.5 AI优化器 (AI Optimizer) - 新增核心组件**
    *   **职责**: 利用AI/机器学习技术自动寻找策略的最优参数组合，或辅助发现新的交易模式，是传统策略研发的强大补充。
    *   **工作流**:
        1.  用户在GUI界面中选择一个待优化的策略和需要搜索的参数范围（如SMA的周期范围`[10, 60]`）。
        2.  GUI向AI优化器模块下达一个优化任务。
        3.  AI优化器（为不阻塞主程序，将运行在独立的子进程中）启动优化流程，核心采用**贝叶斯优化、遗传算法**等先进技术（强烈推荐使用 `Optuna` 库来实现）。
        4.  在每一次评估迭代中，AI优化器会生成一组新的参数，并调用**回测引擎**来运行该参数下的策略，然后获取回测产出的关键绩效指标（如夏普比率、最大回撤、年化收益率）。
        5.  优化算法根据返回的绩效，智能地调整参数，进行下一轮迭代，不断逼近最优解。
        6.  优化任务完成后，将最终的最佳参数、优化过程的可视化图表（如参数关系图、收敛过程图）返回给GUI展示给用户。

#### 2.3. 交易执行与投资组合模块 (Trade Execution & Portfolio Management) - 系统的手臂

该模块整合了订单生命周期管理、资产状态跟踪和风险控制，是连接决策与行动的关键环节。

*   **2.3.1 投资组合管理器 (Portfolio Manager)**
    *   **职责**: 作为系统的**唯一状态中心 (Single Source of Truth)**，精确维护账户的实时状态。它订阅 `SignalEvent`（来自策略引擎的交易意图）和 `FillEvent`（来自交易接口的成交回报），并基于这些信息，权威地管理和计算当前的**现金、持仓详情、冻结资金、账户市值、浮动盈亏**等核心资产数据。
    *   **产出**: 当任何资产状态发生变化时，它会主动发布 `PositionUpdateEvent` (持仓更新) 和 `AccountUpdateEvent` (账户资金更新) 事件。GUI的各个显示组件和风险管理模块将订阅这些事件以实时更新。

*   **2.3.2 订单执行器 (Order Executor)**
    *   **职责**: 扮演从“意图”到“请求”的转换器。它订阅 `SignalEvent`，将其转换为具体、完整的 `OrderRequest`（订单请求）。
    *   **核心逻辑**: 在将订单请求实际发送给券商之前，它必须先将该请求发送给**风险管理器**进行事前检查。此外，这里可以内置更高级的执行逻辑，如大单拆分、算法执行（如TWAP/VWAP）等。

*   **2.3.3 交易接口适配器 (Broker Adapters)**
    *   **职责**: 与数据接入层类似，采用**适配器模式**。将内部统一的交易指令（买、卖、撤单）转换为具体券商或交易所API的格式并发送。反向地，它负责监听券商API的各种回调（如订单状态更新、成交回报），并将这些信息统一翻译成内部标准的事件，如 `FillEvent` (成交事件), `OrderUpdateEvent` (订单状态更新事件)，然后发布到事件总线。

*   **2.3.4 风险管理器 (Risk Manager)**
    *   **职责**: 扮演系统的“安全官”，执行事前和事后风控。它订阅 `OrderRequest` (用于事前风控) 和 `PositionUpdateEvent` (用于事后监控)。
    *   **事前风控 (Pre-Trade)**: 在订单发送到券商**之前**的最后一道防线。检查项包括：单笔订单最大金额/数量、单日最大下单次数、持仓上限、亏损限制、黑名单股票等。若检查失败，则直接拒绝订单，并发布一个 `OrderRejectedEvent`。
    *   **事后监控 (Post-Trade)**: 实时监控整个投资组合的风险暴露。它会持续跟踪总风险敞口、投资组合的最大回撤、保证金水平等关键指标。若触及预设阈值，可发布 `RiskWarningEvent`，此事件可触发GUI的醒目警报、声音提示，甚至在极端情况下自动执行紧急平仓信号。

#### 2.4. 核心引擎 (Core Engine) - 系统的中枢神经

*   **2.4.1 事件总线 (Event Bus)**
    *   **职责**: 系统的心跳和通信骨架。所有模块间的解耦都通过它来完成。
    *   **机制**: 模块A发布一个事件（如`NewBarEvent`），模块B、C订阅了这个事件，总线就会将事件推送给B和C。模块之间互不知道对方的存在，实现了高度的模块化和可扩展性。
    *   **常见事件**: `TickEvent`, `BarEvent`, `OrderSignalEvent`, `OrderUpdateEvent`, `FillEvent`, `PositionUpdateEvent`, `AccountUpdateEvent`, `LogEvent`, `RiskWarningEvent`。
    *   **增强设计**: 为确保事件处理的**严格时序性**并避免在多线程环境下的竞态条件，所有发布的事件不直接调用回调函数，而是先被放入一个**全局的、线程安全的事件队列**（如 Python 的 `queue.Queue`）。由一个专用的事件分发线程按“先进先出”的顺序从队列中取出事件，并分发给所有订阅者。

*   **2.4.2 主控/调度器 (Main Controller / Bootstrap)**
    *   **职责**: 整个程序的入口点 (`main.py`) 和总指挥。负责程序的启动、关闭，初始化所有模块，并将它们有机地组织起来。其核心职责包括：
        1.  **依赖注入 (Dependency Injection)**: 实例化所有核心模块对象（如数据服务桥、投资组合管理器、策略引擎、风险管理器等）。
        2.  **事件注册**: 将各个模块的事件处理函数（Handlers）与对应的事件类型在事件总线上进行订阅注册。例如: `event_bus.subscribe(BarEvent, strategy_engine.on_bar)` 和 `event_bus.subscribe(SignalEvent, portfolio_manager.on_signal)`。
        3.  **启动服务**: 按预定顺序启动各个组件，包括启动数据服务子进程、AI优化器子进程（如果需要）、主事件循环以及GUI主窗口。
        4.  **优雅关闭 (Graceful Shutdown)**: 捕获程序退出信号（如 `Ctrl+C` 或关闭窗口），执行清理流程，安全地通知并关闭所有子进程和后台线程，确保状态（如持仓、配置）能够被正确保存。

#### 2.5. GUI 展现层 (GUI Presentation Layer) - 系统的脸面

*   **职责**: 提供用户交互界面。这一层应该是“瘦”的，只负责展示数据和发送用户指令，不应包含复杂的业务逻辑。
*   **设计模式**: 强烈推荐 **MVVM (Model-View-ViewModel)** 模式，并利用 **PySide6/PyQt** 的信号与槽机制进行完美实现。
    *   **Model (模型)**: 这里的“模型”并非一个单独的类，而是指我们后台的所有业务模块（如投资组合管理器、策略引擎等）。它们是数据的源头，通过事件总线暴露其状态和产出。
    *   **View (视图)**: UI界面本身，由一系列“哑”的Qt控件 (`QWidget`) 构成，如表格、按钮、图表等。它们只负责显示数据和布局，不包含任何业务逻辑。例如，一个 `PositionsTable(QTableWidget)`。
    *   **ViewModel (视图模型)**: GUI与后台业务逻辑之间的核心桥梁。每个复杂的UI组件都对应一个ViewModel。
        *   **职责**:
            1.  **订阅后台事件**: ViewModel订阅事件总线上的相关事件（如 `PositionUpdateEvent`, `LogEvent`）。
            2.  **处理并转发数据**: 当收到后台事件时，ViewModel会处理、格式化数据，然后**通过Qt的信号 (`Signal`) 发射出去**。
            3.  **连接视图**: View控件的槽函数 (`Slot`) 连接到对应ViewModel的信号。例如：`view_model.position_updated_signal.connect(positions_table.update_data)`。这样，后台数据变化就能自动驱动UI刷新。
            4.  **处理用户输入**: ViewModel接收来自View的用户操作（如点击“买入”按钮），将其翻译成内部指令（如 `SendOrderCommand` 或直接调用后台接口），发送给核心引擎处理。
*   **关键组件实现**:
    *   **主窗口 (`QMainWindow`)**: 使用可停靠的 `QDockWidget` 来构建，允许用户自由拖拽、组合、保存工作区布局。
    *   **图表 (`pyqtgraph`)**: 创建一个 `ChartViewModel`，它订阅 `BarEvent` (用于实时更新) 并通过数据API加载历史数据，驱动 `pyqtgraph` 组件进行渲染。
    *   **交易/持仓/日志视图 (`QTableView`, `QTextEdit`)**: 各自对应一个ViewModel，分别订阅 `OrderUpdateEvent`, `PositionUpdateEvent`, `LogEvent` 等事件来刷新显示。
    *   **GUI通知**: 创建一个 `NotificationViewModel`，订阅 `RiskWarningEvent`, `FillEvent` 等重要事件，然后调用一个自定义的桌面弹窗控件或系统级通知来提醒用户。

#### 2.6. 基础设施模块 (Infrastructure) - 系统的工具箱

*   **职责**: 提供被所有其他模块共享的通用功能。
*   **组件**:
    *   **配置管理**: 读取和保存用户配置（API密钥、数据库路径、窗口布局等）。
    *   **日志服务**: 统一的日志记录，支持不同级别（Info, Warn, Error）。为实现UI界面的实时日志显示，日志服务在将日志写入本地文件的同时，还应将日志记录封装成 `LogEvent` 并发布到事件总线。
    *   **任务调度**: 用于定时任务，如每日收盘后自动下载数据、生成日报等。
    *   **通知服务**: 向用户推送重要通知（如成交回报、风控警报），可通过UI弹窗、声音、甚至邮件或微信推送（通过集成第三方服务）。

---

### 3. 项目代码结构 (可执行的蓝图)


quant_trader/
├── main.py                 # 程序入口，执行引导程序
├── core/
│   ├── event_bus.py        # 事件总线实现
│   ├── main_controller.py  # 主控制器/引导程序
│   ├── data_types.py       # 定义BarData, TickData, OrderData等核心数据结构
│   └── event_types.py      # 定义所有事件类 (BarEvent, SignalEvent, ...)
│
├── data_service/
│   ├── process.py          # 数据服务子进程的启动和管理
│   ├── processor.py        # K线合成器等
│   ├── storage.py          # 数据存取API (Feather/Parquet)
│   └── connectors/
│       ├── base_connector.py
│       ├── ctp_connector.py
│       └── binance_connector.py
│
├── strategy_ai_engine/
│   ├── backtester.py       # 回测引擎
│   ├── live_trader.py      # 实盘引擎
│   ├── ai_optimizer.py     # AI优化器 (使用Optuna)
│   ├── strategy_loader.py  # 策略加载器
│   └── indicators/         # 指标库封装
│
├── execution_portfolio/
│   ├── portfolio_manager.py # 投资组合与资金状态管理
│   ├── order_executor.py   # 订单执行逻辑
│   ├── risk_manager.py     # 风控模块
│   └── brokers/
│       ├── base_broker.py
│       └── ctp_broker.py
│
├── gui/
│   ├── main_window.py      # 主窗口和布局
│   ├── viewmodels/         # 所有ViewModel类
│   │   ├── chart_vm.py
│   │   └── portfolio_vm.py
│   ├── views/              # 所有View控件 (QWidget)
│   │   ├── chart_view.py
│   │   └── portfolio_view.py
│   └── widgets/            # 可复用的自定义控件
│
├── infrastructure/
│   ├── config_manager.py   # 配置服务
│   ├── logger.py           # 日志服务
│   └── notification.py     # 通知服务
│
└── strategies/             # 用户策略存放目录
    ├── base_strategy.py    # 策略基类
    └── sma_crossover.py    # 示例策略

### 总结

这份经过强化的架构设计，不仅采纳了你所有的优秀思想，还通过引入**明确的并发模型、进程隔离、集成的AI优化器、MVVM模式的具象化以及可执行的代码结构**，使其更加健壮、专业和易于实现。

这套架构遵循了《软件设计哲学》的核心——**将复杂性深度隐藏**。例如，策略开发者只需关注 `on_bar` 逻辑，无需关心数据从何而来、订单如何执行、GUI如何更新。每个模块都像一个黑盒子，只通过定义良好的接口（事件）与外界通信。

现在，你可以基于这份蓝图，开始逐个模块地进行编码实现了。这套设计足以支撑一个功能强大且高度可扩展的个人量化交易平台。